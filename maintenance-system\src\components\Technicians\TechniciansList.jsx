import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  Avatar,
  Fab,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import dataStorage from '../../data/storage';

const TechniciansList = () => {
  const [technicians, setTechnicians] = useState([]);
  const [filteredTechnicians, setFilteredTechnicians] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadTechnicians();
  }, []);

  useEffect(() => {
    filterTechnicians();
  }, [technicians, searchTerm]);

  const loadTechnicians = () => {
    const data = dataStorage.getTechnicians();
    setTechnicians(data);
  };

  const filterTechnicians = () => {
    if (!searchTerm) {
      setFilteredTechnicians(technicians);
    } else {
      const filtered = technicians.filter(tech =>
        tech.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        tech.department.toLowerCase().includes(searchTerm.toLowerCase()) ||
        tech.specialization.some(spec => 
          spec.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
      setFilteredTechnicians(filtered);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'available': return 'success';
      case 'busy': return 'warning';
      case 'on_leave': return 'info';
      case 'inactive': return 'error';
      default: return 'default';
    }
  };

  const getSkillLevelColor = (level) => {
    switch (level) {
      case 'expert': return 'success';
      case 'advanced': return 'info';
      case 'intermediate': return 'warning';
      case 'beginner': return 'default';
      default: return 'default';
    }
  };

  const handleDelete = (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الفني؟')) {
      // TODO: تنفيذ حذف الفني
      console.log('Delete technician:', id);
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" fontWeight="bold">
          إدارة الفنيين
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => {/* TODO: فتح نموذج إضافة فني */}}
        >
          إضافة فني جديد
        </Button>
      </Box>

      {/* شريط البحث */}
      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          placeholder="البحث في الفنيين..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      {/* جدول الفنيين */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>الفني</TableCell>
              <TableCell>القسم</TableCell>
              <TableCell>التخصص</TableCell>
              <TableCell>مستوى المهارة</TableCell>
              <TableCell>الحالة</TableCell>
              <TableCell>عبء العمل</TableCell>
              <TableCell>المهام المكتملة</TableCell>
              <TableCell>الإجراءات</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredTechnicians.map((tech) => (
              <TableRow key={tech.id} hover>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                      {tech.name.charAt(0)}
                    </Avatar>
                    <Box>
                      <Typography variant="body2" fontWeight="bold">
                        {tech.name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {tech.email}
                      </Typography>
                    </Box>
                  </Box>
                </TableCell>
                <TableCell>{tech.department}</TableCell>
                <TableCell>
                  <Box>
                    {tech.specialization.map((spec, index) => (
                      <Chip
                        key={index}
                        label={spec}
                        size="small"
                        variant="outlined"
                        sx={{ mr: 0.5, mb: 0.5 }}
                      />
                    ))}
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip
                    label={tech.skillLevel}
                    color={getSkillLevelColor(tech.skillLevel)}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Chip
                    label={tech.status}
                    color={getStatusColor(tech.status)}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {tech.workload} / {tech.maxWorkload}
                  </Typography>
                </TableCell>
                <TableCell>{tech.completedTasks}</TableCell>
                <TableCell>
                  <IconButton
                    size="small"
                    color="primary"
                    onClick={() => {/* TODO: فتح نموذج التعديل */}}
                  >
                    <EditIcon />
                  </IconButton>
                  <IconButton
                    size="small"
                    color="error"
                    onClick={() => handleDelete(tech.id)}
                  >
                    <DeleteIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {filteredTechnicians.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h6" color="text.secondary">
            لا يوجد فنيين
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {searchTerm ? 'لم يتم العثور على نتائج للبحث' : 'ابدأ بإضافة فني جديد'}
          </Typography>
        </Box>
      )}

      {/* زر الإضافة العائم */}
      <Fab
        color="primary"
        aria-label="add"
        sx={{ position: 'fixed', bottom: 16, left: 16 }}
        onClick={() => {/* TODO: فتح نموذج إضافة فني */}}
      >
        <AddIcon />
      </Fab>
    </Box>
  );
};

export default TechniciansList;
