import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  LinearProgress,
  Fab,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Inventory as InventoryIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import dataStorage from '../../data/storage';

const InventoryList = () => {
  const [spareParts, setSpareParts] = useState([]);
  const [filteredSpareParts, setFilteredSpareParts] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadSpareParts();
  }, []);

  useEffect(() => {
    filterSpareParts();
  }, [spareParts, searchTerm]);

  const loadSpareParts = () => {
    const data = dataStorage.getSpareParts();
    setSpareParts(data);
  };

  const filterSpareParts = () => {
    if (!searchTerm) {
      setFilteredSpareParts(spareParts);
    } else {
      const filtered = spareParts.filter(part =>
        part.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        part.partNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        part.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
        part.manufacturer.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredSpareParts(filtered);
    }
  };

  const getStockStatus = (part) => {
    if (part.isOutOfStock()) return { label: 'نفد المخزون', color: 'error' };
    if (part.needsReorder()) return { label: 'مخزون منخفض', color: 'warning' };
    return { label: 'متوفر', color: 'success' };
  };

  const getStockPercentage = (part) => {
    return (part.currentStock / part.maximumStock) * 100;
  };

  const handleDelete = (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذه القطعة؟')) {
      // TODO: تنفيذ حذف قطعة الغيار
      console.log('Delete spare part:', id);
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" fontWeight="bold">
          إدارة المخزون
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => {/* TODO: فتح نموذج إضافة قطعة غيار */}}
        >
          إضافة قطعة غيار
        </Button>
      </Box>

      {/* شريط البحث */}
      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          placeholder="البحث في قطع الغيار..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      {/* جدول قطع الغيار */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>اسم القطعة</TableCell>
              <TableCell>رقم القطعة</TableCell>
              <TableCell>الفئة</TableCell>
              <TableCell>المخزون الحالي</TableCell>
              <TableCell>حالة المخزون</TableCell>
              <TableCell>السعر</TableCell>
              <TableCell>الموقع</TableCell>
              <TableCell>الإجراءات</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredSpareParts.map((part) => {
              const stockStatus = getStockStatus(part);
              const stockPercentage = getStockPercentage(part);
              
              return (
                <TableRow key={part.id} hover>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <InventoryIcon sx={{ mr: 1, color: 'primary.main' }} />
                      <Box>
                        <Typography variant="body2" fontWeight="bold">
                          {part.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {part.manufacturer}
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>{part.partNumber}</TableCell>
                  <TableCell>{part.category}</TableCell>
                  <TableCell>
                    <Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Typography variant="body2" sx={{ mr: 1 }}>
                          {part.currentStock} {part.unit}
                        </Typography>
                        {part.needsReorder() && (
                          <WarningIcon color="warning" fontSize="small" />
                        )}
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={stockPercentage}
                        color={stockStatus.color}
                        sx={{ height: 6, borderRadius: 3 }}
                      />
                      <Typography variant="caption" color="text.secondary">
                        الحد الأدنى: {part.minimumStock} {part.unit}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={stockStatus.label}
                      color={stockStatus.color}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {part.unitPrice} {part.currency}
                    </Typography>
                  </TableCell>
                  <TableCell>{part.location}</TableCell>
                  <TableCell>
                    <IconButton
                      size="small"
                      color="primary"
                      onClick={() => {/* TODO: فتح نموذج التعديل */}}
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => handleDelete(part.id)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>

      {filteredSpareParts.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h6" color="text.secondary">
            لا توجد قطع غيار
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {searchTerm ? 'لم يتم العثور على نتائج للبحث' : 'ابدأ بإضافة قطعة غيار جديدة'}
          </Typography>
        </Box>
      )}

      {/* زر الإضافة العائم */}
      <Fab
        color="primary"
        aria-label="add"
        sx={{ position: 'fixed', bottom: 16, left: 16 }}
        onClick={() => {/* TODO: فتح نموذج إضافة قطعة غيار */}}
      >
        <AddIcon />
      </Fab>
    </Box>
  );
};

export default InventoryList;
