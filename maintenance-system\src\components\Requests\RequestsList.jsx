import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  Fab,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  Search as SearchIcon,
  Assignment as AssignmentIcon,
} from '@mui/icons-material';
import dataStorage from '../../data/storage';

const RequestsList = () => {
  const [requests, setRequests] = useState([]);
  const [filteredRequests, setFilteredRequests] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadRequests();
  }, []);

  useEffect(() => {
    filterRequests();
  }, [requests, searchTerm]);

  const loadRequests = () => {
    const data = dataStorage.getRequests();
    setRequests(data);
  };

  const filterRequests = () => {
    if (!searchTerm) {
      setFilteredRequests(requests);
    } else {
      const filtered = requests.filter(req =>
        req.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        req.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        req.requestedBy.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredRequests(filtered);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'warning';
      case 'assigned': return 'info';
      case 'in_progress': return 'primary';
      case 'completed': return 'success';
      case 'cancelled': return 'error';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'urgent': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  const formatDate = (date) => {
    if (!date) return 'غير محدد';
    return new Date(date).toLocaleDateString('ar-SA');
  };

  const getEquipmentName = (equipmentId) => {
    const equipment = dataStorage.getEquipment().find(eq => eq.id === equipmentId);
    return equipment ? equipment.name : 'غير محدد';
  };

  const getTechnicianName = (technicianId) => {
    if (!technicianId) return 'غير معين';
    const technician = dataStorage.getTechnicians().find(tech => tech.id === technicianId);
    return technician ? technician.name : 'غير محدد';
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" fontWeight="bold">
          طلبات الصيانة
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => {/* TODO: فتح نموذج إضافة طلب */}}
        >
          طلب صيانة جديد
        </Button>
      </Box>

      {/* شريط البحث */}
      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          placeholder="البحث في طلبات الصيانة..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      {/* جدول الطلبات */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>عنوان الطلب</TableCell>
              <TableCell>المعدة</TableCell>
              <TableCell>الأولوية</TableCell>
              <TableCell>الحالة</TableCell>
              <TableCell>طالب الصيانة</TableCell>
              <TableCell>الفني المعين</TableCell>
              <TableCell>تاريخ الطلب</TableCell>
              <TableCell>الإجراءات</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredRequests.map((request) => (
              <TableRow key={request.id} hover>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <AssignmentIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Box>
                      <Typography variant="body2" fontWeight="bold">
                        {request.title}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {request.type}
                      </Typography>
                    </Box>
                  </Box>
                </TableCell>
                <TableCell>{getEquipmentName(request.equipmentId)}</TableCell>
                <TableCell>
                  <Chip
                    label={request.priority}
                    color={getPriorityColor(request.priority)}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Chip
                    label={request.status}
                    color={getStatusColor(request.status)}
                    size="small"
                  />
                </TableCell>
                <TableCell>{request.requestedBy}</TableCell>
                <TableCell>{getTechnicianName(request.assignedTo)}</TableCell>
                <TableCell>{formatDate(request.requestDate)}</TableCell>
                <TableCell>
                  <IconButton
                    size="small"
                    color="primary"
                    onClick={() => {/* TODO: عرض تفاصيل الطلب */}}
                  >
                    <ViewIcon />
                  </IconButton>
                  <IconButton
                    size="small"
                    color="secondary"
                    onClick={() => {/* TODO: فتح نموذج التعديل */}}
                  >
                    <EditIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {filteredRequests.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h6" color="text.secondary">
            لا توجد طلبات صيانة
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {searchTerm ? 'لم يتم العثور على نتائج للبحث' : 'ابدأ بإنشاء طلب صيانة جديد'}
          </Typography>
        </Box>
      )}

      {/* زر الإضافة العائم */}
      <Fab
        color="primary"
        aria-label="add"
        sx={{ position: 'fixed', bottom: 16, left: 16 }}
        onClick={() => {/* TODO: فتح نموذج إضافة طلب */}}
      >
        <AddIcon />
      </Fab>
    </Box>
  );
};

export default RequestsList;
