import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  LinearProgress,
  IconButton,
} from '@mui/material';
import {
  Build as BuildIcon,
  Assignment as AssignmentIcon,
  People as PeopleIcon,
  Inventory as InventoryIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  TrendingUp as TrendingUpIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import dataStorage from '../../data/storage';

const Dashboard = () => {
  const [stats, setStats] = useState({});
  const [recentRequests, setRecentRequests] = useState([]);
  const [upcomingMaintenance, setUpcomingMaintenance] = useState([]);
  const [lowStockItems, setLowStockItems] = useState([]);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = () => {
    // تحميل الإحصائيات
    const dashboardStats = dataStorage.getStats();
    setStats(dashboardStats);

    // تحميل طلبات الصيانة الحديثة
    const requests = dataStorage.getRequests()
      .sort((a, b) => new Date(b.requestDate) - new Date(a.requestDate))
      .slice(0, 5);
    setRecentRequests(requests);

    // تحميل المعدات التي تحتاج صيانة قريبة
    const equipment = dataStorage.getEquipment()
      .filter(eq => eq.nextMaintenanceDate)
      .sort((a, b) => new Date(a.nextMaintenanceDate) - new Date(b.nextMaintenanceDate))
      .slice(0, 5);
    setUpcomingMaintenance(equipment);

    // تحميل قطع الغيار منخفضة المخزون
    const spareParts = dataStorage.getSpareParts()
      .filter(part => part.needsReorder())
      .slice(0, 5);
    setLowStockItems(spareParts);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'warning';
      case 'in_progress': return 'info';
      case 'completed': return 'success';
      case 'cancelled': return 'error';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'urgent': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  const formatDate = (date) => {
    if (!date) return 'غير محدد';
    return new Date(date).toLocaleDateString('ar-SA');
  };

  const StatCard = ({ title, value, icon, color = 'primary', subtitle }) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box
            sx={{
              p: 1,
              borderRadius: 1,
              backgroundColor: `${color}.light`,
              color: `${color}.contrastText`,
              mr: 2,
            }}
          >
            {icon}
          </Box>
          <Box>
            <Typography variant="h4" component="div" fontWeight="bold">
              {value}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {title}
            </Typography>
            {subtitle && (
              <Typography variant="caption" color="text.secondary">
                {subtitle}
              </Typography>
            )}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" fontWeight="bold">
          لوحة التحكم
        </Typography>
        <IconButton onClick={loadDashboardData} color="primary">
          <RefreshIcon />
        </IconButton>
      </Box>

      {/* بطاقات الإحصائيات */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="إجمالي المعدات"
            value={stats.totalEquipment || 0}
            icon={<BuildIcon />}
            color="primary"
            subtitle={`${stats.activeEquipment || 0} نشط`}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="طلبات الصيانة"
            value={stats.totalRequests || 0}
            icon={<AssignmentIcon />}
            color="secondary"
            subtitle={`${stats.pendingRequests || 0} في الانتظار`}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="الفنيين"
            value={stats.totalTechnicians || 0}
            icon={<PeopleIcon />}
            color="success"
            subtitle={`${stats.availableTechnicians || 0} متاح`}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="قطع الغيار"
            value={stats.totalSpareParts || 0}
            icon={<InventoryIcon />}
            color="warning"
            subtitle={`${stats.lowStockParts || 0} مخزون منخفض`}
          />
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* طلبات الصيانة الحديثة */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: 400 }}>
            <Typography variant="h6" gutterBottom>
              طلبات الصيانة الحديثة
            </Typography>
            <List>
              {recentRequests.map((request) => (
                <ListItem key={request.id} divider>
                  <ListItemIcon>
                    <AssignmentIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText
                    primary={request.title}
                    secondary={
                      <Box>
                        <Typography variant="caption" display="block">
                          {formatDate(request.requestDate)}
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 1, mt: 0.5 }}>
                          <Chip
                            label={request.status}
                            size="small"
                            color={getStatusColor(request.status)}
                          />
                          <Chip
                            label={request.priority}
                            size="small"
                            color={getPriorityColor(request.priority)}
                          />
                        </Box>
                      </Box>
                    }
                  />
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>

        {/* الصيانة القادمة */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: 400 }}>
            <Typography variant="h6" gutterBottom>
              الصيانة القادمة
            </Typography>
            <List>
              {upcomingMaintenance.map((equipment) => {
                const daysUntil = equipment.getDaysUntilMaintenance();
                return (
                  <ListItem key={equipment.id} divider>
                    <ListItemIcon>
                      {daysUntil <= 3 ? (
                        <WarningIcon color="error" />
                      ) : daysUntil <= 7 ? (
                        <ScheduleIcon color="warning" />
                      ) : (
                        <CheckCircleIcon color="success" />
                      )}
                    </ListItemIcon>
                    <ListItemText
                      primary={equipment.name}
                      secondary={
                        <Box>
                          <Typography variant="caption" display="block">
                            {formatDate(equipment.nextMaintenanceDate)}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {daysUntil > 0 ? `${daysUntil} يوم متبقي` : 'متأخر'}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                );
              })}
            </List>
          </Paper>
        </Grid>

        {/* المخزون المنخفض */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: 300 }}>
            <Typography variant="h6" gutterBottom>
              مخزون منخفض
            </Typography>
            <List>
              {lowStockItems.map((item) => (
                <ListItem key={item.id} divider>
                  <ListItemIcon>
                    <WarningIcon color="warning" />
                  </ListItemIcon>
                  <ListItemText
                    primary={item.name}
                    secondary={
                      <Box>
                        <Typography variant="caption" display="block">
                          المخزون الحالي: {item.currentStock} {item.unit}
                        </Typography>
                        <LinearProgress
                          variant="determinate"
                          value={(item.currentStock / item.maximumStock) * 100}
                          color="warning"
                          sx={{ mt: 0.5 }}
                        />
                      </Box>
                    }
                  />
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>

        {/* إحصائيات سريعة */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: 300 }}>
            <Typography variant="h6" gutterBottom>
              إحصائيات سريعة
            </Typography>
            <Box sx={{ mt: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                <Typography variant="body2">معدل إكمال الطلبات</Typography>
                <Typography variant="body2" fontWeight="bold">
                  {stats.totalRequests > 0 
                    ? Math.round((stats.completedRequests / stats.totalRequests) * 100)
                    : 0}%
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={stats.totalRequests > 0 
                  ? (stats.completedRequests / stats.totalRequests) * 100
                  : 0}
                color="success"
                sx={{ mb: 3 }}
              />

              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                <Typography variant="body2">المعدات في الصيانة</Typography>
                <Typography variant="body2" fontWeight="bold">
                  {stats.equipmentInMaintenance || 0}
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={stats.totalEquipment > 0 
                  ? (stats.equipmentInMaintenance / stats.totalEquipment) * 100
                  : 0}
                color="warning"
                sx={{ mb: 3 }}
              />

              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                <Typography variant="body2">الفنيين المتاحين</Typography>
                <Typography variant="body2" fontWeight="bold">
                  {stats.availableTechnicians || 0} / {stats.totalTechnicians || 0}
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={stats.totalTechnicians > 0 
                  ? (stats.availableTechnicians / stats.totalTechnicians) * 100
                  : 0}
                color="info"
              />
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
