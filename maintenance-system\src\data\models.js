// نماذج البيانات لنظام إدارة الصيانة

// نموذج المعدات
export class Equipment {
  constructor(data = {}) {
    this.id = data.id || null;
    this.name = data.name || '';
    this.type = data.type || '';
    this.model = data.model || '';
    this.serialNumber = data.serialNumber || '';
    this.manufacturer = data.manufacturer || '';
    this.purchaseDate = data.purchaseDate || null;
    this.warrantyExpiry = data.warrantyExpiry || null;
    this.location = data.location || '';
    this.department = data.department || '';
    this.status = data.status || 'active'; // active, inactive, maintenance, retired
    this.condition = data.condition || 'good'; // excellent, good, fair, poor
    this.lastMaintenanceDate = data.lastMaintenanceDate || null;
    this.nextMaintenanceDate = data.nextMaintenanceDate || null;
    this.maintenanceInterval = data.maintenanceInterval || 30; // بالأيام
    this.notes = data.notes || '';
    this.createdAt = data.createdAt || new Date();
    this.updatedAt = data.updatedAt || new Date();
  }

  // حساب الأيام المتبقية للصيانة القادمة
  getDaysUntilMaintenance() {
    if (!this.nextMaintenanceDate) return null;
    const today = new Date();
    const nextMaintenance = new Date(this.nextMaintenanceDate);
    const diffTime = nextMaintenance - today;
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  // تحديث تاريخ الصيانة القادمة
  updateNextMaintenanceDate() {
    if (this.lastMaintenanceDate && this.maintenanceInterval) {
      const lastMaintenance = new Date(this.lastMaintenanceDate);
      const nextMaintenance = new Date(lastMaintenance);
      nextMaintenance.setDate(lastMaintenance.getDate() + this.maintenanceInterval);
      this.nextMaintenanceDate = nextMaintenance;
    }
  }
}

// نموذج طلبات الصيانة
export class MaintenanceRequest {
  constructor(data = {}) {
    this.id = data.id || null;
    this.equipmentId = data.equipmentId || null;
    this.title = data.title || '';
    this.description = data.description || '';
    this.priority = data.priority || 'medium'; // low, medium, high, urgent
    this.type = data.type || 'corrective'; // preventive, corrective, emergency
    this.status = data.status || 'pending'; // pending, assigned, in_progress, completed, cancelled
    this.requestedBy = data.requestedBy || '';
    this.assignedTo = data.assignedTo || null;
    this.requestDate = data.requestDate || new Date();
    this.scheduledDate = data.scheduledDate || null;
    this.startDate = data.startDate || null;
    this.completionDate = data.completionDate || null;
    this.estimatedDuration = data.estimatedDuration || null; // بالساعات
    this.actualDuration = data.actualDuration || null; // بالساعات
    this.cost = data.cost || 0;
    this.partsUsed = data.partsUsed || []; // مصفوفة من قطع الغيار المستخدمة
    this.workPerformed = data.workPerformed || '';
    this.notes = data.notes || '';
    this.attachments = data.attachments || [];
    this.createdAt = data.createdAt || new Date();
    this.updatedAt = data.updatedAt || new Date();
  }

  // حساب مدة التأخير
  getDelayDays() {
    if (!this.scheduledDate || this.status === 'completed') return 0;
    const today = new Date();
    const scheduled = new Date(this.scheduledDate);
    if (today > scheduled) {
      const diffTime = today - scheduled;
      return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
    return 0;
  }

  // تحديث الحالة
  updateStatus(newStatus, userId = null) {
    this.status = newStatus;
    this.updatedAt = new Date();
    
    if (newStatus === 'in_progress' && !this.startDate) {
      this.startDate = new Date();
    } else if (newStatus === 'completed' && !this.completionDate) {
      this.completionDate = new Date();
      if (this.startDate) {
        const diffTime = this.completionDate - this.startDate;
        this.actualDuration = diffTime / (1000 * 60 * 60); // بالساعات
      }
    }
  }
}

// نموذج الفنيين
export class Technician {
  constructor(data = {}) {
    this.id = data.id || null;
    this.name = data.name || '';
    this.email = data.email || '';
    this.phone = data.phone || '';
    this.department = data.department || '';
    this.specialization = data.specialization || [];
    this.skillLevel = data.skillLevel || 'intermediate'; // beginner, intermediate, advanced, expert
    this.status = data.status || 'available'; // available, busy, on_leave, inactive
    this.workload = data.workload || 0; // عدد المهام المعينة حالياً
    this.maxWorkload = data.maxWorkload || 5;
    this.hourlyRate = data.hourlyRate || 0;
    this.totalHoursWorked = data.totalHoursWorked || 0;
    this.completedTasks = data.completedTasks || 0;
    this.averageRating = data.averageRating || 0;
    this.joinDate = data.joinDate || new Date();
    this.lastActiveDate = data.lastActiveDate || new Date();
    this.notes = data.notes || '';
    this.createdAt = data.createdAt || new Date();
    this.updatedAt = data.updatedAt || new Date();
  }

  // التحقق من توفر الفني
  isAvailable() {
    return this.status === 'available' && this.workload < this.maxWorkload;
  }

  // إضافة مهمة جديدة
  assignTask() {
    if (this.isAvailable()) {
      this.workload++;
      this.status = this.workload >= this.maxWorkload ? 'busy' : 'available';
      this.updatedAt = new Date();
      return true;
    }
    return false;
  }

  // إكمال مهمة
  completeTask(hoursWorked = 0) {
    if (this.workload > 0) {
      this.workload--;
      this.completedTasks++;
      this.totalHoursWorked += hoursWorked;
      this.status = 'available';
      this.lastActiveDate = new Date();
      this.updatedAt = new Date();
    }
  }
}

// نموذج قطع الغيار
export class SparePart {
  constructor(data = {}) {
    this.id = data.id || null;
    this.name = data.name || '';
    this.partNumber = data.partNumber || '';
    this.description = data.description || '';
    this.category = data.category || '';
    this.manufacturer = data.manufacturer || '';
    this.supplier = data.supplier || '';
    this.unitPrice = data.unitPrice || 0;
    this.currency = data.currency || 'USD';
    this.currentStock = data.currentStock || 0;
    this.minimumStock = data.minimumStock || 0;
    this.maximumStock = data.maximumStock || 100;
    this.reorderPoint = data.reorderPoint || 10;
    this.location = data.location || '';
    this.unit = data.unit || 'piece'; // piece, meter, liter, kg, etc.
    this.lastOrderDate = data.lastOrderDate || null;
    this.lastUsedDate = data.lastUsedDate || null;
    this.expiryDate = data.expiryDate || null;
    this.notes = data.notes || '';
    this.createdAt = data.createdAt || new Date();
    this.updatedAt = data.updatedAt || new Date();
  }

  // التحقق من الحاجة لإعادة الطلب
  needsReorder() {
    return this.currentStock <= this.reorderPoint;
  }

  // التحقق من نفاد المخزون
  isOutOfStock() {
    return this.currentStock <= 0;
  }

  // استخدام قطعة غيار
  useStock(quantity) {
    if (this.currentStock >= quantity) {
      this.currentStock -= quantity;
      this.lastUsedDate = new Date();
      this.updatedAt = new Date();
      return true;
    }
    return false;
  }

  // إضافة مخزون
  addStock(quantity) {
    this.currentStock += quantity;
    this.updatedAt = new Date();
  }
}

// نموذج تقارير الصيانة
export class MaintenanceReport {
  constructor(data = {}) {
    this.id = data.id || null;
    this.title = data.title || '';
    this.type = data.type || 'monthly'; // daily, weekly, monthly, quarterly, yearly
    this.startDate = data.startDate || null;
    this.endDate = data.endDate || null;
    this.totalRequests = data.totalRequests || 0;
    this.completedRequests = data.completedRequests || 0;
    this.pendingRequests = data.pendingRequests || 0;
    this.totalCost = data.totalCost || 0;
    this.totalHours = data.totalHours || 0;
    this.averageCompletionTime = data.averageCompletionTime || 0;
    this.equipmentDowntime = data.equipmentDowntime || 0;
    this.topIssues = data.topIssues || [];
    this.technicianPerformance = data.technicianPerformance || [];
    this.costBreakdown = data.costBreakdown || {};
    this.generatedBy = data.generatedBy || '';
    this.generatedAt = data.generatedAt || new Date();
  }
}

// حالات النظام
export const EQUIPMENT_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  MAINTENANCE: 'maintenance',
  RETIRED: 'retired'
};

export const EQUIPMENT_CONDITION = {
  EXCELLENT: 'excellent',
  GOOD: 'good',
  FAIR: 'fair',
  POOR: 'poor'
};

export const REQUEST_PRIORITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  URGENT: 'urgent'
};

export const REQUEST_STATUS = {
  PENDING: 'pending',
  ASSIGNED: 'assigned',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
};

export const REQUEST_TYPE = {
  PREVENTIVE: 'preventive',
  CORRECTIVE: 'corrective',
  EMERGENCY: 'emergency'
};

export const TECHNICIAN_STATUS = {
  AVAILABLE: 'available',
  BUSY: 'busy',
  ON_LEAVE: 'on_leave',
  INACTIVE: 'inactive'
};

export const SKILL_LEVEL = {
  BEGINNER: 'beginner',
  INTERMEDIATE: 'intermediate',
  ADVANCED: 'advanced',
  EXPERT: 'expert'
};
