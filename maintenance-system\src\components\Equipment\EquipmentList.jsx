import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  Fab,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Build as BuildIcon,
} from '@mui/icons-material';
import dataStorage from '../../data/storage';

const EquipmentList = () => {
  const [equipment, setEquipment] = useState([]);
  const [filteredEquipment, setFilteredEquipment] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadEquipment();
  }, []);

  useEffect(() => {
    filterEquipment();
  }, [equipment, searchTerm]);

  const loadEquipment = () => {
    const data = dataStorage.getEquipment();
    setEquipment(data);
  };

  const filterEquipment = () => {
    if (!searchTerm) {
      setFilteredEquipment(equipment);
    } else {
      const filtered = equipment.filter(eq =>
        eq.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        eq.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
        eq.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
        eq.department.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredEquipment(filtered);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'success';
      case 'inactive': return 'default';
      case 'maintenance': return 'warning';
      case 'retired': return 'error';
      default: return 'default';
    }
  };

  const getConditionColor = (condition) => {
    switch (condition) {
      case 'excellent': return 'success';
      case 'good': return 'info';
      case 'fair': return 'warning';
      case 'poor': return 'error';
      default: return 'default';
    }
  };

  const formatDate = (date) => {
    if (!date) return 'غير محدد';
    return new Date(date).toLocaleDateString('ar-SA');
  };

  const handleDelete = (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذا المعدة؟')) {
      dataStorage.deleteEquipment(id);
      loadEquipment();
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" fontWeight="bold">
          إدارة المعدات
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => {/* TODO: فتح نموذج إضافة معدة */}}
        >
          إضافة معدة جديدة
        </Button>
      </Box>

      {/* شريط البحث */}
      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          placeholder="البحث في المعدات..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      {/* جدول المعدات */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>اسم المعدة</TableCell>
              <TableCell>النوع</TableCell>
              <TableCell>الموقع</TableCell>
              <TableCell>القسم</TableCell>
              <TableCell>الحالة</TableCell>
              <TableCell>الوضع</TableCell>
              <TableCell>الصيانة القادمة</TableCell>
              <TableCell>الإجراءات</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredEquipment.map((eq) => (
              <TableRow key={eq.id} hover>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <BuildIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Box>
                      <Typography variant="body2" fontWeight="bold">
                        {eq.name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {eq.model} - {eq.serialNumber}
                      </Typography>
                    </Box>
                  </Box>
                </TableCell>
                <TableCell>{eq.type}</TableCell>
                <TableCell>{eq.location}</TableCell>
                <TableCell>{eq.department}</TableCell>
                <TableCell>
                  <Chip
                    label={eq.status}
                    color={getStatusColor(eq.status)}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Chip
                    label={eq.condition}
                    color={getConditionColor(eq.condition)}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  {eq.nextMaintenanceDate ? (
                    <Box>
                      <Typography variant="body2">
                        {formatDate(eq.nextMaintenanceDate)}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {eq.getDaysUntilMaintenance() > 0 
                          ? `${eq.getDaysUntilMaintenance()} يوم متبقي`
                          : 'متأخر'
                        }
                      </Typography>
                    </Box>
                  ) : (
                    'غير محدد'
                  )}
                </TableCell>
                <TableCell>
                  <IconButton
                    size="small"
                    color="primary"
                    onClick={() => {/* TODO: فتح نموذج التعديل */}}
                  >
                    <EditIcon />
                  </IconButton>
                  <IconButton
                    size="small"
                    color="error"
                    onClick={() => handleDelete(eq.id)}
                  >
                    <DeleteIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {filteredEquipment.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h6" color="text.secondary">
            لا توجد معدات
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {searchTerm ? 'لم يتم العثور على نتائج للبحث' : 'ابدأ بإضافة معدة جديدة'}
          </Typography>
        </Box>
      )}

      {/* زر الإضافة العائم */}
      <Fab
        color="primary"
        aria-label="add"
        sx={{ position: 'fixed', bottom: 16, left: 16 }}
        onClick={() => {/* TODO: فتح نموذج إضافة معدة */}}
      >
        <AddIcon />
      </Fab>
    </Box>
  );
};

export default EquipmentList;
