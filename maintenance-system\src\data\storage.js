// إدارة البيانات المحلية لنظام الصيانة
import { Equipment, MaintenanceRequest, Technician, SparePart } from './models.js';

class DataStorage {
  constructor() {
    this.storageKeys = {
      equipment: 'maintenance_equipment',
      requests: 'maintenance_requests',
      technicians: 'maintenance_technicians',
      spareParts: 'maintenance_spare_parts',
      settings: 'maintenance_settings'
    };
    
    // تهيئة البيانات الأولية إذا لم تكن موجودة
    this.initializeData();
  }

  // تهيئة البيانات الأولية
  initializeData() {
    if (!this.getEquipment().length) {
      this.createSampleData();
    }
  }

  // إنشاء بيانات تجريبية
  createSampleData() {
    // معدات تجريبية
    const sampleEquipment = [
      new Equipment({
        id: 1,
        name: 'مولد كهربائي رئيسي',
        type: 'مولد كهربائي',
        model: 'CAT-3516C',
        serialNumber: 'GEN001',
        manufacturer: 'Caterpillar',
        location: 'غرفة المولدات',
        department: 'الكهرباء',
        status: 'active',
        condition: 'good',
        maintenanceInterval: 30
      }),
      new Equipment({
        id: 2,
        name: 'مكيف هواء مركزي',
        type: 'تكييف',
        model: 'YORK-YK',
        serialNumber: 'AC001',
        manufacturer: 'York',
        location: 'السطح',
        department: 'التكييف',
        status: 'active',
        condition: 'excellent',
        maintenanceInterval: 90
      }),
      new Equipment({
        id: 3,
        name: 'مصعد ركاب',
        type: 'مصعد',
        model: 'OTIS-2000',
        serialNumber: 'ELV001',
        manufacturer: 'Otis',
        location: 'البرج الشرقي',
        department: 'المصاعد',
        status: 'maintenance',
        condition: 'fair',
        maintenanceInterval: 15
      })
    ];

    // فنيين تجريبيين
    const sampleTechnicians = [
      new Technician({
        id: 1,
        name: 'أحمد محمد',
        email: '<EMAIL>',
        phone: '0501234567',
        department: 'الكهرباء',
        specialization: ['مولدات', 'أنظمة كهربائية'],
        skillLevel: 'advanced',
        status: 'available',
        hourlyRate: 50
      }),
      new Technician({
        id: 2,
        name: 'محمد علي',
        email: '<EMAIL>',
        phone: '0507654321',
        department: 'التكييف',
        specialization: ['تكييف مركزي', 'تبريد'],
        skillLevel: 'expert',
        status: 'available',
        hourlyRate: 60
      }),
      new Technician({
        id: 3,
        name: 'خالد أحمد',
        email: '<EMAIL>',
        phone: '0509876543',
        department: 'المصاعد',
        specialization: ['مصاعد', 'أنظمة ميكانيكية'],
        skillLevel: 'intermediate',
        status: 'busy',
        workload: 3,
        hourlyRate: 45
      })
    ];

    // قطع غيار تجريبية
    const sampleSpareParts = [
      new SparePart({
        id: 1,
        name: 'فلتر هواء',
        partNumber: 'AIR-FILTER-001',
        category: 'فلاتر',
        manufacturer: 'Mann Filter',
        unitPrice: 25,
        currentStock: 15,
        minimumStock: 5,
        reorderPoint: 8,
        unit: 'piece'
      }),
      new SparePart({
        id: 2,
        name: 'زيت محرك',
        partNumber: 'OIL-15W40-001',
        category: 'زيوت',
        manufacturer: 'Shell',
        unitPrice: 8,
        currentStock: 50,
        minimumStock: 10,
        reorderPoint: 15,
        unit: 'liter'
      }),
      new SparePart({
        id: 3,
        name: 'حبل مصعد',
        partNumber: 'ROPE-STEEL-001',
        category: 'حبال',
        manufacturer: 'Otis',
        unitPrice: 150,
        currentStock: 2,
        minimumStock: 1,
        reorderPoint: 2,
        unit: 'meter'
      })
    ];

    // طلبات صيانة تجريبية
    const sampleRequests = [
      new MaintenanceRequest({
        id: 1,
        equipmentId: 1,
        title: 'صيانة دورية للمولد الرئيسي',
        description: 'تغيير الزيت والفلاتر والفحص العام',
        priority: 'medium',
        type: 'preventive',
        status: 'assigned',
        requestedBy: 'مدير الصيانة',
        assignedTo: 1,
        scheduledDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
        estimatedDuration: 4
      }),
      new MaintenanceRequest({
        id: 2,
        equipmentId: 3,
        title: 'إصلاح عطل في المصعد',
        description: 'المصعد لا يتحرك إلى الطابق الثالث',
        priority: 'high',
        type: 'corrective',
        status: 'in_progress',
        requestedBy: 'أمن المبنى',
        assignedTo: 3,
        startDate: new Date(),
        estimatedDuration: 6
      })
    ];

    // حفظ البيانات التجريبية
    this.saveEquipment(sampleEquipment);
    this.saveTechnicians(sampleTechnicians);
    this.saveSpareParts(sampleSpareParts);
    this.saveRequests(sampleRequests);
  }

  // وظائف إدارة المعدات
  getEquipment() {
    const data = localStorage.getItem(this.storageKeys.equipment);
    if (!data) return [];
    return JSON.parse(data).map(item => new Equipment(item));
  }

  saveEquipment(equipment) {
    localStorage.setItem(this.storageKeys.equipment, JSON.stringify(equipment));
  }

  addEquipment(equipment) {
    const allEquipment = this.getEquipment();
    equipment.id = this.generateId(allEquipment);
    allEquipment.push(equipment);
    this.saveEquipment(allEquipment);
    return equipment;
  }

  updateEquipment(id, updatedData) {
    const allEquipment = this.getEquipment();
    const index = allEquipment.findIndex(eq => eq.id === id);
    if (index !== -1) {
      allEquipment[index] = { ...allEquipment[index], ...updatedData, updatedAt: new Date() };
      this.saveEquipment(allEquipment);
      return allEquipment[index];
    }
    return null;
  }

  deleteEquipment(id) {
    const allEquipment = this.getEquipment();
    const filtered = allEquipment.filter(eq => eq.id !== id);
    this.saveEquipment(filtered);
    return true;
  }

  // وظائف إدارة طلبات الصيانة
  getRequests() {
    const data = localStorage.getItem(this.storageKeys.requests);
    if (!data) return [];
    return JSON.parse(data).map(item => new MaintenanceRequest(item));
  }

  saveRequests(requests) {
    localStorage.setItem(this.storageKeys.requests, JSON.stringify(requests));
  }

  addRequest(request) {
    const allRequests = this.getRequests();
    request.id = this.generateId(allRequests);
    allRequests.push(request);
    this.saveRequests(allRequests);
    return request;
  }

  updateRequest(id, updatedData) {
    const allRequests = this.getRequests();
    const index = allRequests.findIndex(req => req.id === id);
    if (index !== -1) {
      allRequests[index] = { ...allRequests[index], ...updatedData, updatedAt: new Date() };
      this.saveRequests(allRequests);
      return allRequests[index];
    }
    return null;
  }

  // وظائف إدارة الفنيين
  getTechnicians() {
    const data = localStorage.getItem(this.storageKeys.technicians);
    if (!data) return [];
    return JSON.parse(data).map(item => new Technician(item));
  }

  saveTechnicians(technicians) {
    localStorage.setItem(this.storageKeys.technicians, JSON.stringify(technicians));
  }

  addTechnician(technician) {
    const allTechnicians = this.getTechnicians();
    technician.id = this.generateId(allTechnicians);
    allTechnicians.push(technician);
    this.saveTechnicians(allTechnicians);
    return technician;
  }

  updateTechnician(id, updatedData) {
    const allTechnicians = this.getTechnicians();
    const index = allTechnicians.findIndex(tech => tech.id === id);
    if (index !== -1) {
      allTechnicians[index] = { ...allTechnicians[index], ...updatedData, updatedAt: new Date() };
      this.saveTechnicians(allTechnicians);
      return allTechnicians[index];
    }
    return null;
  }

  // وظائف إدارة قطع الغيار
  getSpareParts() {
    const data = localStorage.getItem(this.storageKeys.spareParts);
    if (!data) return [];
    return JSON.parse(data).map(item => new SparePart(item));
  }

  saveSpareParts(spareParts) {
    localStorage.setItem(this.storageKeys.spareParts, JSON.stringify(spareParts));
  }

  addSparePart(sparePart) {
    const allSpareParts = this.getSpareParts();
    sparePart.id = this.generateId(allSpareParts);
    allSpareParts.push(sparePart);
    this.saveSpareParts(allSpareParts);
    return sparePart;
  }

  updateSparePart(id, updatedData) {
    const allSpareParts = this.getSpareParts();
    const index = allSpareParts.findIndex(part => part.id === id);
    if (index !== -1) {
      allSpareParts[index] = { ...allSpareParts[index], ...updatedData, updatedAt: new Date() };
      this.saveSpareParts(allSpareParts);
      return allSpareParts[index];
    }
    return null;
  }

  // وظائف مساعدة
  generateId(array) {
    if (array.length === 0) return 1;
    return Math.max(...array.map(item => item.id || 0)) + 1;
  }

  // مسح جميع البيانات
  clearAllData() {
    Object.values(this.storageKeys).forEach(key => {
      localStorage.removeItem(key);
    });
  }

  // إحصائيات سريعة
  getStats() {
    const equipment = this.getEquipment();
    const requests = this.getRequests();
    const technicians = this.getTechnicians();
    const spareParts = this.getSpareParts();

    return {
      totalEquipment: equipment.length,
      activeEquipment: equipment.filter(eq => eq.status === 'active').length,
      equipmentInMaintenance: equipment.filter(eq => eq.status === 'maintenance').length,
      totalRequests: requests.length,
      pendingRequests: requests.filter(req => req.status === 'pending').length,
      inProgressRequests: requests.filter(req => req.status === 'in_progress').length,
      completedRequests: requests.filter(req => req.status === 'completed').length,
      totalTechnicians: technicians.length,
      availableTechnicians: technicians.filter(tech => tech.status === 'available').length,
      totalSpareParts: spareParts.length,
      lowStockParts: spareParts.filter(part => part.needsReorder()).length
    };
  }
}

// إنشاء مثيل واحد للاستخدام في التطبيق
export const dataStorage = new DataStorage();
export default dataStorage;
