import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  DatePicker,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  Assessment as ReportsIcon,
  Download as DownloadIcon,
  Print as PrintIcon,
  TrendingUp as TrendingUpIcon,
  Build as BuildIcon,
  Assignment as AssignmentIcon,
  People as PeopleIcon,
} from '@mui/icons-material';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import dataStorage from '../../data/storage';

const Reports = () => {
  const [reportType, setReportType] = useState('summary');
  const [startDate, setStartDate] = useState(dayjs().subtract(30, 'day'));
  const [endDate, setEndDate] = useState(dayjs());
  const [reportData, setReportData] = useState(null);

  useEffect(() => {
    generateReport();
  }, [reportType, startDate, endDate]);

  const generateReport = () => {
    const equipment = dataStorage.getEquipment();
    const requests = dataStorage.getRequests();
    const technicians = dataStorage.getTechnicians();
    const spareParts = dataStorage.getSpareParts();

    // فلترة البيانات حسب التاريخ
    const filteredRequests = requests.filter(req => {
      const reqDate = dayjs(req.requestDate);
      return reqDate.isAfter(startDate) && reqDate.isBefore(endDate);
    });

    const data = {
      summary: {
        totalEquipment: equipment.length,
        activeEquipment: equipment.filter(eq => eq.status === 'active').length,
        totalRequests: filteredRequests.length,
        completedRequests: filteredRequests.filter(req => req.status === 'completed').length,
        pendingRequests: filteredRequests.filter(req => req.status === 'pending').length,
        totalTechnicians: technicians.length,
        availableTechnicians: technicians.filter(tech => tech.status === 'available').length,
        lowStockItems: spareParts.filter(part => part.needsReorder()).length,
      },
      equipmentReport: equipment.map(eq => ({
        name: eq.name,
        type: eq.type,
        status: eq.status,
        condition: eq.condition,
        lastMaintenance: eq.lastMaintenanceDate,
        nextMaintenance: eq.nextMaintenanceDate,
        requestsCount: requests.filter(req => req.equipmentId === eq.id).length,
      })),
      technicianReport: technicians.map(tech => ({
        name: tech.name,
        department: tech.department,
        completedTasks: tech.completedTasks,
        workload: tech.workload,
        totalHours: tech.totalHoursWorked,
        averageRating: tech.averageRating,
      })),
      maintenanceReport: filteredRequests.map(req => ({
        title: req.title,
        equipment: equipment.find(eq => eq.id === req.equipmentId)?.name || 'غير محدد',
        technician: technicians.find(tech => tech.id === req.assignedTo)?.name || 'غير معين',
        status: req.status,
        priority: req.priority,
        requestDate: req.requestDate,
        completionDate: req.completionDate,
        cost: req.cost,
        duration: req.actualDuration,
      })),
    };

    setReportData(data);
  };

  const handleExport = () => {
    // TODO: تنفيذ تصدير التقرير
    console.log('Export report');
  };

  const handlePrint = () => {
    window.print();
  };

  const renderSummaryReport = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <BuildIcon color="primary" sx={{ mr: 2 }} />
              <Box>
                <Typography variant="h4">{reportData.summary.totalEquipment}</Typography>
                <Typography color="text.secondary">إجمالي المعدات</Typography>
                <Typography variant="caption">
                  {reportData.summary.activeEquipment} نشط
                </Typography>
              </Box>
            </Box>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <AssignmentIcon color="secondary" sx={{ mr: 2 }} />
              <Box>
                <Typography variant="h4">{reportData.summary.totalRequests}</Typography>
                <Typography color="text.secondary">طلبات الصيانة</Typography>
                <Typography variant="caption">
                  {reportData.summary.completedRequests} مكتمل
                </Typography>
              </Box>
            </Box>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <PeopleIcon color="success" sx={{ mr: 2 }} />
              <Box>
                <Typography variant="h4">{reportData.summary.totalTechnicians}</Typography>
                <Typography color="text.secondary">الفنيين</Typography>
                <Typography variant="caption">
                  {reportData.summary.availableTechnicians} متاح
                </Typography>
              </Box>
            </Box>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <TrendingUpIcon color="warning" sx={{ mr: 2 }} />
              <Box>
                <Typography variant="h4">
                  {reportData.summary.totalRequests > 0 
                    ? Math.round((reportData.summary.completedRequests / reportData.summary.totalRequests) * 100)
                    : 0}%
                </Typography>
                <Typography color="text.secondary">معدل الإنجاز</Typography>
                <Typography variant="caption">
                  {reportData.summary.lowStockItems} مخزون منخفض
                </Typography>
              </Box>
            </Box>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const renderTableReport = (data, columns) => (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            {columns.map((column) => (
              <TableCell key={column.key}>{column.label}</TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {data.map((row, index) => (
            <TableRow key={index}>
              {columns.map((column) => (
                <TableCell key={column.key}>
                  {column.format ? column.format(row[column.key]) : row[column.key]}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  const getReportContent = () => {
    if (!reportData) return null;

    switch (reportType) {
      case 'summary':
        return renderSummaryReport();
      case 'equipment':
        return renderTableReport(reportData.equipmentReport, [
          { key: 'name', label: 'اسم المعدة' },
          { key: 'type', label: 'النوع' },
          { key: 'status', label: 'الحالة' },
          { key: 'condition', label: 'الوضع' },
          { key: 'requestsCount', label: 'عدد الطلبات' },
          { 
            key: 'lastMaintenance', 
            label: 'آخر صيانة',
            format: (date) => date ? new Date(date).toLocaleDateString('ar-SA') : 'غير محدد'
          },
        ]);
      case 'technicians':
        return renderTableReport(reportData.technicianReport, [
          { key: 'name', label: 'اسم الفني' },
          { key: 'department', label: 'القسم' },
          { key: 'completedTasks', label: 'المهام المكتملة' },
          { key: 'workload', label: 'عبء العمل' },
          { key: 'totalHours', label: 'إجمالي الساعات' },
          { key: 'averageRating', label: 'متوسط التقييم' },
        ]);
      case 'maintenance':
        return renderTableReport(reportData.maintenanceReport, [
          { key: 'title', label: 'عنوان الطلب' },
          { key: 'equipment', label: 'المعدة' },
          { key: 'technician', label: 'الفني' },
          { key: 'status', label: 'الحالة' },
          { key: 'priority', label: 'الأولوية' },
          { 
            key: 'requestDate', 
            label: 'تاريخ الطلب',
            format: (date) => new Date(date).toLocaleDateString('ar-SA')
          },
          { key: 'cost', label: 'التكلفة' },
        ]);
      default:
        return null;
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1" fontWeight="bold">
            التقارير والإحصائيات
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              startIcon={<PrintIcon />}
              onClick={handlePrint}
            >
              طباعة
            </Button>
            <Button
              variant="contained"
              startIcon={<DownloadIcon />}
              onClick={handleExport}
            >
              تصدير
            </Button>
          </Box>
        </Box>

        {/* فلاتر التقرير */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth>
                <InputLabel>نوع التقرير</InputLabel>
                <Select
                  value={reportType}
                  label="نوع التقرير"
                  onChange={(e) => setReportType(e.target.value)}
                >
                  <MenuItem value="summary">ملخص عام</MenuItem>
                  <MenuItem value="equipment">تقرير المعدات</MenuItem>
                  <MenuItem value="technicians">تقرير الفنيين</MenuItem>
                  <MenuItem value="maintenance">تقرير الصيانة</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <DatePicker
                label="من تاريخ"
                value={startDate}
                onChange={setStartDate}
                slotProps={{ textField: { fullWidth: true } }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <DatePicker
                label="إلى تاريخ"
                value={endDate}
                onChange={setEndDate}
                slotProps={{ textField: { fullWidth: true } }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Button
                variant="contained"
                fullWidth
                onClick={generateReport}
                startIcon={<ReportsIcon />}
              >
                إنشاء التقرير
              </Button>
            </Grid>
          </Grid>
        </Paper>

        {/* محتوى التقرير */}
        <Box>
          {getReportContent()}
        </Box>
      </Box>
    </LocalizationProvider>
  );
};

export default Reports;
